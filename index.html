<!DOCTYPE html>
<html lang="en" data-theme="corporate">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forever Living Business Documentation</title>
    <meta name="description" content="Complete Forever Living Business Training Documentation & Success Strategies">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Tailwind CSS + DaisyUI -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.19/dist/full.min.css" rel="stylesheet" type="text/css" />

    <!-- Chart.js for graphs -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'mono': ['JetBrains Mono', 'monospace'],
                    }
                }
            }
        }
    </script>

    <style>
        .doc-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        .doc-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        .sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
        }
        .content-area {
            background: #ffffff;
        }
        .nav-item {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-bottom: 0.25rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .nav-item:hover {
            background: #e2e8f0;
        }
        .nav-item.active {
            background: #3b82f6;
            color: white;
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .progress-ring-circle {
            transition: stroke-dashoffset 0.35s;
            transform-origin: 50% 50%;
        }
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
            margin-bottom: 2rem;
        }
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #3b82f6;
        }
        .step-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            position: relative;
        }
        .step-number {
            position: absolute;
            top: -12px;
            left: 1.5rem;
            background: #3b82f6;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
        }
        .badge-success {
            background: #10b981;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .badge-warning {
            background: #f59e0b;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .badge-info {
            background: #3b82f6;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Search Highlighting Styles */
        .search-highlight {
            background: linear-gradient(120deg, #fbbf24 0%, #f59e0b 100%);
            color: #1f2937;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 600;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .search-highlight-pulse {
            animation: highlightPulse 2s ease-in-out;
        }

        @keyframes highlightPulse {
            0%, 100% {
                background: linear-gradient(120deg, #fbbf24 0%, #f59e0b 100%);
                transform: scale(1);
            }
            50% {
                background: linear-gradient(120deg, #f97316 0%, #ea580c 100%);
                transform: scale(1.05);
                box-shadow: 0 0 20px rgba(249, 115, 22, 0.4);
            }
        }

        /* Mobile Optimizations */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 64px;
                left: 0;
                height: calc(100vh - 64px);
                z-index: 40;
                background: white;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            }

            .content-area {
                width: 100%;
                padding: 1rem;
            }

            .highlight-box {
                padding: 1rem;
                margin-bottom: 1rem;
            }

            .metric-card {
                padding: 1rem;
            }

            .step-card {
                padding: 1rem;
            }

            .doc-card {
                padding: 1rem;
            }

            .search-highlight {
                padding: 1px 3px;
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-900">Forever Living</h1>
                        <p class="text-xs text-gray-500">Business Documentation</p>
                    </div>
                </div>

                <!-- Search Icon (Center) -->
                <div class="flex-1 flex justify-center">
                    <button class="btn btn-ghost btn-sm" onclick="toggleSearch()">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Theme Toggle -->
                    <div class="dropdown dropdown-end">
                        <label tabindex="0" class="btn btn-ghost btn-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="theme-icon">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                        </label>
                        <ul tabindex="0" class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-32">
                            <li><a onclick="setTheme('light')">☀️ Light</a></li>
                            <li><a onclick="setTheme('dark')">🌙 Dark</a></li>
                        </ul>
                    </div>

                    <!-- Mobile Menu Toggle -->
                    <button class="btn btn-ghost btn-sm md:hidden" onclick="toggleMobileSidebar()">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Search Modal Overlay -->
    <div class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden" id="search-overlay" onclick="closeSearch()">
        <div class="flex items-start justify-center pt-20">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4" onclick="event.stopPropagation()">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Search Documentation</h3>
                        <button onclick="closeSearch()" class="btn btn-ghost btn-sm">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="relative">
                        <input type="text" placeholder="Search across all documentation..."
                               class="input input-bordered w-full pl-10 pr-4" id="search-input" autofocus>
                        <svg class="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <div class="mt-4 text-sm text-gray-500" id="search-suggestions">
                        <p class="mb-3">Popular searches:</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('3C Formula')">3C Formula</span>
                            <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Faiz Alam')">Faiz Alam</span>
                            <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Krishna Sir')">Krishna Sir</span>
                            <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Manager')">Manager</span>
                            <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Level Up')">Level Up</span>
                            <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('2CC')">2CC</span>
                            <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Hot Market')">Hot Market</span>
                            <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Daily Action')">Daily Action</span>
                            <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Achievers Club')">Achievers Club</span>
                            <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Personal Brand')">Personal Brand</span>
                        </div>
                        <p class="mt-3 text-xs text-gray-400">Press <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl+K</kbd> to search from anywhere</p>
                    </div>
                    <div id="search-results" class="mt-4 max-h-80 overflow-y-auto">
                        <!-- Search results will appear here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Question Modal -->
    <div class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden" id="add-question-modal" onclick="closeAddQuestionModal()">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl" onclick="event.stopPropagation()">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-gray-900">Add New Question & Answer</h3>
                        <button onclick="closeAddQuestionModal()" class="btn btn-ghost btn-sm">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <form id="question-form" onsubmit="handleQuestionSubmit(event)">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Question</label>
                                <input type="text" id="question-input" required
                                       class="input input-bordered w-full"
                                       placeholder="Enter your question here...">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Answer</label>
                                <textarea id="answer-input" required rows="6"
                                          class="textarea textarea-bordered w-full"
                                          placeholder="Enter the detailed answer here..."></textarea>
                            </div>

                            <div class="flex justify-end space-x-3 pt-4">
                                <button type="button" onclick="closeAddQuestionModal()"
                                        class="btn btn-outline">Cancel</button>
                                <button type="submit" class="btn btn-primary">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    Add Question
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- PIN Verification Modal -->
    <div class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden" id="pin-modal" onclick="closePinModal()">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-md" onclick="event.stopPropagation()">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">PIN Verification Required</h3>
                        <button onclick="closePinModal()" class="btn btn-ghost btn-sm">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <p class="text-gray-600">Enter PIN to add question to the database</p>
                    </div>

                    <form id="pin-form" onsubmit="handlePinSubmit(event)">
                        <div class="space-y-4">
                            <div>
                                <input type="password" id="pin-input" required maxlength="4"
                                       class="input input-bordered w-full text-center text-2xl tracking-widest"
                                       placeholder="••••" autocomplete="off">
                                <p class="text-xs text-gray-500 mt-2 text-center">Enter 4-digit PIN</p>
                            </div>

                            <div id="pin-error" class="text-red-600 text-sm text-center hidden">
                                Incorrect PIN. Please try again.
                            </div>

                            <div class="flex justify-end space-x-3 pt-4">
                                <button type="button" onclick="closePinModal()"
                                        class="btn btn-outline">Cancel</button>
                                <button type="submit" class="btn btn-primary">
                                    Verify PIN
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="flex h-screen pt-16">
        <!-- Sidebar -->
        <aside class="w-80 sidebar overflow-y-auto md:block hidden" id="sidebar">
            <div class="p-6">
                <!-- Progress Overview -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Learning Progress</h3>
                    <div class="flex items-center justify-center mb-4">
                        <div class="relative w-24 h-24">
                            <svg class="progress-ring w-24 h-24" viewBox="0 0 120 120">
                                <circle cx="60" cy="60" r="54" fill="transparent" stroke="#e5e7eb" stroke-width="8"/>
                                <circle cx="60" cy="60" r="54" fill="transparent" stroke="#3b82f6" stroke-width="8"
                                        stroke-dasharray="339.292" stroke-dashoffset="0" class="progress-ring-circle"/>
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-xl font-bold text-gray-900">100%</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600 text-center">6 of 6 modules completed</p>
                </div>

                <!-- Navigation -->
                <nav class="space-y-1">
                    <div class="nav-item active" onclick="showSection('overview')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <span class="font-medium">Overview</span>
                        </div>
                    </div>

                    <div class="nav-item" onclick="showSection('secret-formula')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"></path>
                            </svg>
                            <span class="font-medium">Secret Formula</span>
                        </div>
                    </div>

                    <div class="nav-item" onclick="showSection('training-system')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <span class="font-medium">Training System</span>
                        </div>
                    </div>

                    <div class="nav-item" onclick="showSection('success-stories')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            <span class="font-medium">Success Stories</span>
                        </div>
                    </div>

                    <div class="nav-item" onclick="showSection('level-up')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                            <span class="font-medium">Level Up Strategy</span>
                        </div>
                    </div>

                    <div class="nav-item" onclick="showSection('action-plan')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                            </svg>
                            <span class="font-medium">Action Plan</span>
                        </div>
                    </div>

                    <div class="nav-item" onclick="showSection('query')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-medium">Q&A</span>
                        </div>
                    </div>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 content-area overflow-y-auto">
            <div class="max-w-4xl mx-auto p-8" id="main-content">
                <!-- Content will be loaded here -->
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="content-functions.js"></script>
    <script>
        // Theme management
        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);

            // Update theme icon
            const themeIcon = document.getElementById('theme-icon');
            if (theme === 'dark') {
                themeIcon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>`;
            } else {
                themeIcon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>`;
            }
        }

        // Load saved theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        setTheme(savedTheme);

        // Search functions
        function toggleSearch() {
            const searchOverlay = document.getElementById('search-overlay');
            searchOverlay.classList.remove('hidden');
            setTimeout(() => {
                document.getElementById('search-input').focus();
            }, 100);
        }

        function closeSearch() {
            const searchOverlay = document.getElementById('search-overlay');
            searchOverlay.classList.add('hidden');
            document.getElementById('search-input').value = '';
            document.getElementById('search-results').innerHTML = '';
        }

        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('hidden');
            sidebar.classList.toggle('md:block');
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Close search on Escape
            if (e.key === 'Escape') {
                closeSearch();
            }

            // Open search with Ctrl+K or Cmd+K
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                toggleSearch();
            }
        });

        // Navigation
        function showSection(section) {
            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            // Load content
            loadContent(section);

            // Clear any existing highlights when navigating normally
            setTimeout(() => {
                const content = document.getElementById('main-content');
                if (content) {
                    removeHighlights(content);
                }
            }, 100);
        }

        // Content loader
        function loadContent(section) {
            const content = document.getElementById('main-content');

            switch(section) {
                case 'overview':
                    content.innerHTML = getOverviewContent();
                    break;
                case 'secret-formula':
                    content.innerHTML = getSecretFormulaContent();
                    break;
                case 'training-system':
                    content.innerHTML = getTrainingSystemContent();
                    break;
                case 'success-stories':
                    content.innerHTML = getSuccessStoriesContent();
                    break;
                case 'level-up':
                    content.innerHTML = getLevelUpContent();
                    break;
                case 'action-plan':
                    content.innerHTML = getActionPlanContent();
                    break;
                case 'query':
                    content.innerHTML = getQueryContent();
                    loadQuestions();
                    break;
                default:
                    content.innerHTML = getOverviewContent();
            }

            // Initialize charts if needed
            setTimeout(() => {
                initializeCharts();
            }, 100);
        }

        // Initialize on load
        document.addEventListener('DOMContentLoaded', function() {
            loadContent('overview');

            // Add click listener to remove highlights
            document.addEventListener('click', function(e) {
                // Don't remove highlights if clicking on search elements
                if (e.target.closest('#search-overlay') ||
                    e.target.closest('[onclick*="toggleSearch"]') ||
                    e.target.classList.contains('search-highlight')) {
                    return;
                }

                // Remove highlights on any other click
                const content = document.getElementById('main-content');
                if (content) {
                    removeHighlights(content);
                }
            });
        });

        // Search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.addEventListener('input', function(e) {
                    const searchTerm = e.target.value.toLowerCase().trim();
                    performSearch(searchTerm);
                });
            }
        });

        function performSearch(searchTerm) {
            const resultsContainer = document.getElementById('search-results');
            const suggestionsContainer = document.getElementById('search-suggestions');

            if (!searchTerm) {
                resultsContainer.innerHTML = '';
                suggestionsContainer.innerHTML = `
                    <p class="mb-3">Popular searches:</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('3C Formula')">3C Formula</span>
                        <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Faiz Alam')">Faiz Alam</span>
                        <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Krishna Sir')">Krishna Sir</span>
                        <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Manager')">Manager</span>
                        <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Level Up')">Level Up</span>
                        <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('2CC')">2CC</span>
                        <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Hot Market')">Hot Market</span>
                        <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Daily Action')">Daily Action</span>
                        <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Achievers Club')">Achievers Club</span>
                        <span class="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full cursor-pointer transition-colors" onclick="searchFor('Personal Brand')">Personal Brand</span>
                    </div>
                    <p class="mt-3 text-xs text-gray-400">Press <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl+K</kbd> to search from anywhere</p>
                `;
                return;
            }

            // Hide suggestions when searching
            suggestionsContainer.innerHTML = '';

            const sections = [
                { name: 'Overview', key: 'overview', icon: '📊' },
                { name: 'Secret Formula', key: 'secret-formula', icon: '🔐' },
                { name: 'Training System', key: 'training-system', icon: '🎯' },
                { name: 'Success Stories', key: 'success-stories', icon: '⚡' },
                { name: 'Level Up Strategy', key: 'level-up', icon: '📈' },
                { name: 'Action Plan', key: 'action-plan', icon: '📋' },
                { name: 'Q&A', key: 'query', icon: '❓' }
            ];

            let results = [];

            sections.forEach(section => {
                const content = getContentForSearch(section.key);
                const matches = findMatches(content, searchTerm);

                matches.forEach(match => {
                    results.push({
                        section: section.name,
                        key: section.key,
                        icon: section.icon,
                        snippet: match.snippet,
                        context: match.context,
                        relevance: match.relevance
                    });
                });
            });

            // Sort by relevance
            results.sort((a, b) => b.relevance - a.relevance);

            displaySearchResults(results, searchTerm);
        }

        function getContentForSearch(sectionKey) {
            const contentMap = {
                'overview': {
                    title: 'Forever Living Business Documentation',
                    content: 'Complete Forever Living Business Training Documentation Success Strategies. 6 modules total modules success stories action items. Learning Progress 100% completed. Overview Secret Formula Training System Success Stories Level Up Strategy Action Plan. Quick Start Guide new Forever Living business. Read Secret Formula understand core principles. Study Training System daily implementation. Review Success Stories motivation examples. Create Action Plan using provided template. Metrics cards statistics. Module cards clickable navigation.',
                    keywords: ['business', 'training', 'modules', 'success', 'strategy', 'forever living', 'documentation', 'overview', 'progress', 'metrics', 'quick start', 'guide']
                },
                'secret-formula': {
                    title: 'Krishna Sir Secret Formula 3C Strategy',
                    content: 'Sir Ka Secret Formula Krishna Sir proven 3C strategy. Core Principles Excitement full power energy high journey smooth. Clear WHY path clear define. 3C Formula Breakdown Contribution hamesha help karo. Connection team bonding logon judho. Clarity Focus Direction clarity focus milega direction. Level Up Roadmap Novas target 2CC AS level. AS target 10CC Supervisor. Supervisor target Assistant Manager. Manager target Senior Manager. Incentives Recognition 4x7 Incentive har mahine 7 tareekh pehle 4CC business close. Forever official magazine naam recognition milta special benefits. C9 Challenge Fat to Fit transformation dikhao inspire. Golden Rule Forever daily products use khud karo. Business mein lao pure knowledge confidence. Forever wallet system samjho samjhao.',
                    keywords: ['3C', 'formula', 'contribution', 'connection', 'clarity', 'krishna sir', 'excitement', 'why', 'manager', 'marketing plan', 'roadmap', 'incentives', 'recognition', 'fat to fit', 'golden rule']
                },
                'training-system': {
                    title: 'Training System Daily Actions Systematic Approach',
                    content: 'Take Charge Your Journey Build 10CC complete ownership take responsibility growth. Personally generate leads conduct meetings guide team every step. Start Learning Start Earning focus continuous learning quick implementation earning begins. Build personal brand people know who you are what you stand. Train with Applian professional training foundation begins. Follow Culture Right Ethics always give right information. Promote use Forever Products pride become true product. Daily Action Plan Control Your Time apne day CEO bano. Daily Value Addition har roz ek insaan value emotionally connect genuine help daily valuable act share product use product. Write Your WHY har din apne why likho strengthen purpose. System Approach system kaam lo superhero matt bano step step approach. Track Actions roz apne actions time track karo. Training System training system importance. Level Up Dates 5th July 8th July 13th July 16th July 20th July 23rd July 26th July 28th July. TASK From Fat to Fit 1Kit 1CC means 2Kit 2CC 9 Kits. Key Focus Areas Responsible Learning Sacrifice growth Consistency part-time approach Instagram business tool. Product Strategy Use product share experience network share business opportunity.',
                    keywords: ['training', 'daily', '10CC', 'leads', 'meetings', 'personal brand', 'learning', 'earning', 'system', 'time control', 'applian', 'culture', 'ethics', 'fat to fit', 'level up dates', 'instagram']
                },
                'success-stories': {
                    title: 'Success Stories Case Studies Real Examples',
                    content: 'Faiz Alam Empire Builder How did Faiz Alam leave Family Business create Empire. Key Achievements 6CC completed Hot Market June July. 4 Managers Hot Market. 1 Soaring Sakib Faiz mama. 3 Senior Sheetal Kotiyal Kamlesh Verma Palash Bhai. Success Strategy 2CC Mandatory 30000 fixed investment. Hot Market Focus started family friends. Relation Building Krishna Sir mentorship. System Timing new enrollment system launch. Faiz Secret Kyun Nhi Kar Sakte Believe Yourself. Nouman Raza Fastest Sapphire Manager Assistant Supervisor Sapphire Manager record time. Main Tips Marketing Plan Product Knowledge Niyat Saaf Rakho Pure Intentions. 2CC knowledge world best products Purpose Clear Karo Remove Laziness Make Schedule Good Day Plan Support customers dont exploit Create vision downline. Action Formula 10 Callings aise nhi chalega. DO MORE consistency hard work. Practice These Invitation Follow Up Closing. Achievers Club Benefits Be Wise Your Investment. Assistant Supervisor Never Give Up Believe Yourself Long Term Game. Supervisor team development Forever incentives recurring income strategy.',
                    keywords: ['faiz alam', 'empire', 'hot market', 'manager', 'nouman raza', 'sapphire', 'achievers club', 'supervisor', 'success', 'family business', 'sakib', 'sheetal', 'kamlesh', 'palash', 'mentorship', 'enrollment', 'invitation', 'follow up', 'closing']
                },
                'level-up': {
                    title: 'Level Up Strategy Rank Advancement Grand Session',
                    content: 'Grand Level Up Session Rohit Arora Achievers Club. session sirf unke liye apna minimum billing kar chuke. 2CC Combo Offer 5 Benefits Personal Mentorship Krishna Arora roadmap action planning. Achievers Club Course Networking skills personal branding. Upline Mentorship Daily tracking activities guidance. Daily Training System Consistent skill development. Lead Generation System Proven lead generation methods. Manager Mindset Manager bante 1 Lakh check. Forever Products family gift karo. Pehle Aloe Vera baad iPhone. One month income nhi aaye Don Give Up. Long term game patience rakho. Fly High July DO IT. Focus Areas LEVEL UP Focus one thing time. FRONT CC Direct customer acquisition. GOAL SETTING Goal mapping tracking. TEAM COORDINATION Build relationships downline. Social Media Strategy Daily Activities Daily 5 Instagram stories work. Personal pics every 3-4 days. Social media updates regularly. Content creation business. Lead Generation Daily DMs Instagram. Hot Market Reference Market. Har bande paise need. Personal brand building. Common Reasons Leaving Business Marketing Plan Issues understanding compensation structure. Company Policy Khud account paise nhi dalwane. Product vs Prospects Focus products just prospects.',
                    keywords: ['level up', 'rohit arora', '2CC', 'combo', 'mentorship', 'achievers club', 'lead generation', 'manager mindset', 'rank advancement', 'krishna arora', 'networking', 'upline', 'aloe vera', 'iphone', 'instagram', 'social media', 'dms', 'compensation', 'policy']
                },
                'action-plan': {
                    title: '30-Day Action Plan Implementation Self Assessment',
                    content: 'Important Self Assessment Questions Answer honestly create action plan. Key Questions Ask Yourself Why really here today. What will change life take decision today. What stopping success till now. someone guiding experience will trust follow fully. What cost staying same. just one decision away 360 degree change ready. not now then when. 30-Day Action Plan Week 1 Foundation Complete 2CC billing Download My Forever App Complete KYC Aadhaar Study product knowledge. Week 2 Hot Market List 30 prospects Start family friends Share product experience Begin follow-ups. Week 3 Expansion 30 enrollments target Social media content Team building start Daily tracking. Week 4 Optimization Review adjust Mentor feedback Plan next month Celebrate wins. Daily Checklist Morning Routine Write your WHY Plan your day Take Forever products Review goals. Evening Review Track daily actions Follow prospects Social media posts Plan tomorrow. Next Steps Ready start Forever Living journey Ask Your Mentor Level Up Session Download Action Plan Template.',
                    keywords: ['action plan', '30 day', 'self assessment', 'foundation', '2CC billing', 'hot market', 'prospects', 'daily checklist', 'implementation', 'kyc', 'aadhaar', 'forever app', 'product knowledge', 'enrollments', 'social media', 'team building', 'tracking', 'mentor', 'template', 'morning routine', 'evening review']
                },
                'query': {
                    title: 'Questions & Answers Community Help',
                    content: 'Find answers to common questions about Forever Living business. Community-driven Q&A section with detailed answers. Add new questions, get help from experienced members. PIN-protected question submission. Real-time database of business queries and solutions. Ask questions about 3C Formula, training system, success stories, level up strategy, action plans. Get expert answers from community members.',
                    keywords: ['questions', 'answers', 'Q&A', 'help', 'community', 'queries', 'solutions', 'support', 'ask', 'answer', 'database', 'pin', 'submission', 'business help', 'forever living questions', 'expert answers', 'community support']
                }
            };

            return contentMap[sectionKey] || { title: '', content: '', keywords: [] };
        }

        function findMatches(contentObj, searchTerm) {
            const matches = [];
            const term = searchTerm.toLowerCase().trim();
            const words = term.split(/\s+/);

            // Check title match - both exact and word-by-word
            if (contentObj.title.toLowerCase().includes(term)) {
                matches.push({
                    snippet: contentObj.title,
                    context: 'Title',
                    relevance: 10
                });
            } else if (words.length > 1) {
                // Check if all words are present in title
                const titleLower = contentObj.title.toLowerCase();
                if (words.every(word => titleLower.includes(word))) {
                    matches.push({
                        snippet: contentObj.title,
                        context: 'Title',
                        relevance: 9
                    });
                }
            }

            // Check content match - both exact and word-by-word
            if (contentObj.content.toLowerCase().includes(term)) {
                const snippet = extractHighlightedSnippet(contentObj.content, searchTerm);
                matches.push({
                    snippet: snippet,
                    context: 'Content',
                    relevance: 8
                });
            } else if (words.length > 1) {
                // Check if all words are present in content
                const contentLower = contentObj.content.toLowerCase();
                if (words.every(word => contentLower.includes(word))) {
                    const snippet = extractHighlightedSnippetMultiWord(contentObj.content, words);
                    matches.push({
                        snippet: snippet,
                        context: 'Content',
                        relevance: 7
                    });
                }
            }

            // Check individual word matches in content
            words.forEach(word => {
                if (word.length > 2 && contentObj.content.toLowerCase().includes(word)) {
                    const snippet = extractHighlightedSnippet(contentObj.content, word);
                    matches.push({
                        snippet: snippet,
                        context: 'Partial Match',
                        relevance: 5
                    });
                }
            });

            // Check keyword matches
            contentObj.keywords.forEach(keyword => {
                if (keyword.toLowerCase().includes(term)) {
                    matches.push({
                        snippet: `Related to: ${keyword}`,
                        context: 'Keyword',
                        relevance: 6
                    });
                } else if (words.length > 1) {
                    // Check if all words are in keyword
                    const keywordLower = keyword.toLowerCase();
                    if (words.every(word => keywordLower.includes(word))) {
                        matches.push({
                            snippet: `Related to: ${keyword}`,
                            context: 'Keyword',
                            relevance: 5
                        });
                    }
                }
            });

            // Remove duplicates and return unique matches
            const uniqueMatches = matches.filter((match, index, self) =>
                index === self.findIndex(m => m.snippet === match.snippet && m.context === match.context)
            );

            return uniqueMatches;
        }

        function extractHighlightedSnippet(content, searchTerm) {
            const index = content.toLowerCase().indexOf(searchTerm.toLowerCase());
            if (index === -1) return content.substring(0, 100) + '...';

            const start = Math.max(0, index - 40);
            const end = Math.min(content.length, index + searchTerm.length + 40);
            let snippet = content.substring(start, end);

            // Add ellipsis
            if (start > 0) snippet = '...' + snippet;
            if (end < content.length) snippet = snippet + '...';

            return snippet;
        }

        function extractHighlightedSnippetMultiWord(content, words) {
            // Find the first occurrence of any word
            let firstIndex = content.length;
            let foundWord = '';

            words.forEach(word => {
                const index = content.toLowerCase().indexOf(word.toLowerCase());
                if (index !== -1 && index < firstIndex) {
                    firstIndex = index;
                    foundWord = word;
                }
            });

            if (firstIndex === content.length) {
                return content.substring(0, 100) + '...';
            }

            const start = Math.max(0, firstIndex - 40);
            const end = Math.min(content.length, firstIndex + foundWord.length + 40);
            let snippet = content.substring(start, end);

            // Add ellipsis
            if (start > 0) snippet = '...' + snippet;
            if (end < content.length) snippet = snippet + '...';

            return snippet;
        }

        function highlightText(text, searchTerm) {
            if (!searchTerm) return text;

            const words = searchTerm.toLowerCase().trim().split(/\s+/);
            let highlightedText = text;

            // First try exact phrase match
            const exactRegex = new RegExp(`(${escapeRegex(searchTerm)})`, 'gi');
            if (exactRegex.test(text)) {
                return text.replace(exactRegex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
            }

            // Then highlight individual words
            words.forEach(word => {
                if (word.length > 2) {
                    const wordRegex = new RegExp(`(${escapeRegex(word)})`, 'gi');
                    highlightedText = highlightedText.replace(wordRegex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
                }
            });

            return highlightedText;
        }

        function displaySearchResults(results, searchTerm) {
            const resultsContainer = document.getElementById('search-results');

            if (results.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="text-center py-8">
                        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <p class="text-gray-500 text-lg">No results found for "${searchTerm}"</p>
                        <p class="text-gray-400 text-sm mt-2">Try different keywords or check spelling</p>
                    </div>
                `;
                return;
            }

            const resultsHTML = `
                <div class="mb-4">
                    <p class="text-sm text-gray-600">About ${results.length} result${results.length > 1 ? 's' : ''} for <strong>"${searchTerm}"</strong></p>
                </div>
                ${results.map(result => `
                    <div class="border-b border-gray-100 py-4 cursor-pointer hover:bg-blue-50 rounded-lg px-3 -mx-3 transition-colors duration-200" onclick="goToSection('${result.key}')">
                        <div class="flex items-start space-x-3">
                            <span class="text-2xl">${result.icon}</span>
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-1">
                                    <h4 class="font-medium text-blue-600 hover:underline">${result.section}</h4>
                                    <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">${result.context}</span>
                                </div>
                                <p class="text-sm text-gray-700 leading-relaxed">${highlightText(result.snippet, searchTerm)}</p>
                            </div>
                        </div>
                    </div>
                `).join('')}
            `;

            resultsContainer.innerHTML = resultsHTML;
        }

        function searchFor(term) {
            document.getElementById('search-input').value = term;
            performSearch(term);
        }

        function goToSection(sectionKey) {
            const searchTerm = document.getElementById('search-input').value;
            closeSearch();
            loadContent(sectionKey);

            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            const targetNav = document.querySelector(`[onclick="showSection('${sectionKey}')"]`);
            if (targetNav) {
                targetNav.classList.add('active');
            }

            // Highlight search term in content after a short delay
            if (searchTerm) {
                setTimeout(() => {
                    highlightInContent(searchTerm);
                }, 300);
            }
        }

        function highlightInContent(searchTerm) {
            const content = document.getElementById('main-content');
            if (!content || !searchTerm) return;

            // Remove existing highlights
            removeHighlights(content);

            // Add new highlights
            highlightTextInElement(content, searchTerm);

            // Scroll to first highlight
            const firstHighlight = content.querySelector('.search-highlight');
            if (firstHighlight) {
                firstHighlight.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Add pulse animation to first highlight
                firstHighlight.classList.add('search-highlight-pulse');
                setTimeout(() => {
                    firstHighlight.classList.remove('search-highlight-pulse');
                }, 2000);
            }
        }

        function removeHighlights(element) {
            const highlights = element.querySelectorAll('.search-highlight');
            highlights.forEach(highlight => {
                const parent = highlight.parentNode;
                parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                parent.normalize();
            });
        }

        function highlightTextInElement(element, searchTerm) {
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            const words = searchTerm.toLowerCase().trim().split(/\s+/);
            const textNodes = [];
            let node;

            while (node = walker.nextNode()) {
                const nodeText = node.nodeValue.toLowerCase();
                // Check if node contains exact phrase or any of the words
                if (nodeText.includes(searchTerm.toLowerCase()) ||
                    words.some(word => word.length > 2 && nodeText.includes(word))) {
                    textNodes.push(node);
                }
            }

            textNodes.forEach(textNode => {
                const parent = textNode.parentNode;
                if (parent.tagName === 'SCRIPT' || parent.tagName === 'STYLE') return;

                const text = textNode.nodeValue;
                let highlightedText = text;
                let hasMatch = false;

                // First try exact phrase
                const exactRegex = new RegExp(`(${escapeRegex(searchTerm)})`, 'gi');
                if (exactRegex.test(text)) {
                    highlightedText = text;
                    hasMatch = true;

                    const fragment = document.createDocumentFragment();
                    let lastIndex = 0;

                    text.replace(exactRegex, (match, p1, offset) => {
                        // Add text before match
                        if (offset > lastIndex) {
                            fragment.appendChild(document.createTextNode(text.slice(lastIndex, offset)));
                        }

                        // Add highlighted match
                        const highlight = document.createElement('span');
                        highlight.className = 'search-highlight';
                        highlight.textContent = match;
                        fragment.appendChild(highlight);

                        lastIndex = offset + match.length;
                        return match;
                    });

                    // Add remaining text
                    if (lastIndex < text.length) {
                        fragment.appendChild(document.createTextNode(text.slice(lastIndex)));
                    }

                    parent.replaceChild(fragment, textNode);
                } else {
                    // Highlight individual words
                    const fragment = document.createDocumentFragment();
                    let currentText = text;
                    let hasWordMatch = false;

                    words.forEach(word => {
                        if (word.length > 2) {
                            const wordRegex = new RegExp(`(${escapeRegex(word)})`, 'gi');
                            if (wordRegex.test(currentText)) {
                                hasWordMatch = true;
                            }
                        }
                    });

                    if (hasWordMatch) {
                        let processedText = text;
                        words.forEach(word => {
                            if (word.length > 2) {
                                const wordRegex = new RegExp(`(${escapeRegex(word)})`, 'gi');
                                processedText = processedText.replace(wordRegex, `<HIGHLIGHT>$1</HIGHLIGHT>`);
                            }
                        });

                        // Convert back to DOM elements
                        const parts = processedText.split(/<HIGHLIGHT>|<\/HIGHLIGHT>/);
                        for (let i = 0; i < parts.length; i++) {
                            if (i % 2 === 0) {
                                // Regular text
                                if (parts[i]) {
                                    fragment.appendChild(document.createTextNode(parts[i]));
                                }
                            } else {
                                // Highlighted text
                                const highlight = document.createElement('span');
                                highlight.className = 'search-highlight';
                                highlight.textContent = parts[i];
                                fragment.appendChild(highlight);
                            }
                        }

                        parent.replaceChild(fragment, textNode);
                    }
                }
            });
        }

        function escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // Firebase Configuration and Q&A Functions
        const firebaseConfig = {
            apiKey: "AIzaSyBIbM4D4xj8bwMFAC9cfSBg6hucG416ZHM",
            authDomain: "forever-47b8a.firebaseapp.com",
            projectId: "forever-47b8a",
            storageBucket: "forever-47b8a.firebasestorage.app",
            messagingSenderId: "4395604727",
            appId: "1:4395604727:web:1295d5e7ba895365f3d1f2",
            measurementId: "G-EKFJSGC1YB"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        let pendingQuestion = null;
        let pendingAnswer = null;
        let isAdminAuthenticated = false;

        // Heart Click Handler for Admin Access
        function handleHeartClick() {
            if (isAdminAuthenticated) {
                // Already authenticated, show add question modal directly
                openAddQuestionModal();
            } else {
                // Need authentication, show PIN modal
                openPinModalForAuth();
            }
        }

        // Q&A Modal Functions
        function openAddQuestionModal() {
            if (!isAdminAuthenticated) {
                handleHeartClick();
                return;
            }
            document.getElementById('add-question-modal').classList.remove('hidden');
            setTimeout(() => {
                document.getElementById('question-input').focus();
            }, 100);
        }

        function closeAddQuestionModal() {
            document.getElementById('add-question-modal').classList.add('hidden');
            document.getElementById('question-form').reset();
            pendingQuestion = null;
            pendingAnswer = null;
        }

        function handleQuestionSubmit(event) {
            event.preventDefault();

            const question = document.getElementById('question-input').value.trim();
            const answer = document.getElementById('answer-input').value.trim();

            if (!question || !answer) {
                alert('Please fill in both question and answer fields.');
                return;
            }

            // Store the question and answer temporarily
            pendingQuestion = question;
            pendingAnswer = answer;

            // Save directly since user is already authenticated
            saveQuestionToFirebase();
            closeAddQuestionModal();
        }

        // PIN Modal Functions for Authentication
        function openPinModalForAuth() {
            document.getElementById('pin-modal').classList.remove('hidden');
            document.getElementById('pin-error').classList.add('hidden');
            // Update modal text for authentication
            const pinText = document.querySelector('#pin-modal p');
            if (pinText) {
                pinText.textContent = 'Enter PIN to access admin features';
            }
            setTimeout(() => {
                document.getElementById('pin-input').focus();
            }, 100);
        }

        function openPinModal() {
            document.getElementById('pin-modal').classList.remove('hidden');
            document.getElementById('pin-error').classList.add('hidden');
            // Update modal text for question submission
            const pinText = document.querySelector('#pin-modal p');
            if (pinText) {
                pinText.textContent = 'Enter PIN to add question to the database';
            }
            setTimeout(() => {
                document.getElementById('pin-input').focus();
            }, 100);
        }

        function closePinModal() {
            document.getElementById('pin-modal').classList.add('hidden');
            document.getElementById('pin-form').reset();
            document.getElementById('pin-error').classList.add('hidden');
        }

        function handlePinSubmit(event) {
            event.preventDefault();

            const pin = document.getElementById('pin-input').value;
            const correctPin = "7890";

            if (pin === correctPin) {
                // PIN is correct
                isAdminAuthenticated = true;

                // Show Add Question button
                const addQuestionBtn = document.getElementById('add-question-btn');
                if (addQuestionBtn) {
                    addQuestionBtn.classList.remove('hidden');
                }

                // If there's a pending question, save it
                if (pendingQuestion && pendingAnswer) {
                    saveQuestionToFirebase();
                } else {
                    // Just authenticated, open add question modal
                    closePinModal();
                    openAddQuestionModal();
                    return;
                }

                closePinModal();
            } else {
                // Show error
                document.getElementById('pin-error').classList.remove('hidden');
                document.getElementById('pin-input').value = '';
                document.getElementById('pin-input').focus();
            }
        }

        // Firebase Functions
        function saveQuestionToFirebase() {
            if (!pendingQuestion || !pendingAnswer) {
                alert('Error: No question data to save.');
                return;
            }

            console.log('Saving question to Firebase:', { question: pendingQuestion, answer: pendingAnswer });

            // Create question data without timestamp for validation
            const questionData = {
                question: pendingQuestion,
                answer: pendingAnswer
            };

            db.collection('questions').add(questionData)
                .then((docRef) => {
                    console.log('Document written with ID: ', docRef.id);
                    alert('Question added successfully!');
                    pendingQuestion = null;
                    pendingAnswer = null;

                    // Reload questions if we're on the query page
                    const currentContent = document.getElementById('main-content');
                    if (currentContent && currentContent.innerHTML.includes('qa-container')) {
                        setTimeout(() => {
                            loadQuestions();
                        }, 1000); // Small delay to ensure data is available
                    }
                })
                .catch((error) => {
                    console.error('Error saving question:', error);
                    console.error('Error details:', error.code, error.message);
                    alert('Error saving question: ' + error.message);
                });
        }

        function loadQuestions() {
            const container = document.getElementById('qa-container');
            const countElement = document.getElementById('qa-count');

            if (!container) {
                console.log('QA container not found, retrying in 500ms...');
                setTimeout(loadQuestions, 500);
                return;
            }

            console.log('Loading questions from Firestore...');

            db.collection('questions').get()
                .then((querySnapshot) => {
                    console.log('Questions loaded successfully, count:', querySnapshot.size);
                    const questions = [];
                    querySnapshot.forEach((doc) => {
                        const data = doc.data();
                        questions.push({
                            id: doc.id,
                            question: data.question,
                            answer: data.answer,
                            timestamp: data.timestamp ? data.timestamp.toDate() : new Date()
                        });
                    });

                    // Sort by timestamp (newest first) or by document ID if no timestamp
                    questions.sort((a, b) => b.timestamp - a.timestamp);

                    displayQuestions(questions);

                    if (countElement) {
                        countElement.textContent = `${questions.length} question${questions.length !== 1 ? 's' : ''} available`;
                    }
                })
                .catch((error) => {
                    console.error('Error loading questions:', error);
                    console.error('Error details:', error.code, error.message);
                    if (container) {
                        container.innerHTML = `
                            <div class="text-center py-12">
                                <div class="text-red-600 mb-4">
                                    <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <p class="text-gray-600">Error loading questions: ${error.message}</p>
                                <button onclick="loadQuestions()" class="btn btn-sm btn-outline mt-4">Try Again</button>
                            </div>
                        `;
                    }
                });
        }

        function displayQuestions(questions) {
            const container = document.getElementById('qa-container');

            if (questions.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No Questions Yet</h3>
                        <p class="text-gray-600 mb-4">Be the first to add a question to help the community!</p>
                        <button onclick="handleHeartClick()" class="btn btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                            </svg>
                            Add First Question
                        </button>
                    </div>
                `;
                return;
            }

            const questionsHTML = questions.map((qa, index) => `
                <div class="doc-card p-6">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 font-semibold">${index + 1}</span>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">
                                <svg class="w-5 h-5 inline mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                ${qa.question}
                            </h3>
                            <div class="prose prose-sm max-w-none">
                                <div class="bg-gray-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
                                    <p class="text-gray-700 leading-relaxed whitespace-pre-wrap">${qa.answer}</p>
                                </div>
                            </div>
                            ${qa.timestamp ? `
                                <div class="mt-4 text-xs text-gray-500">
                                    Added on ${qa.timestamp.toLocaleDateString()}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = questionsHTML;
        }
    </script>
</body>
</html>