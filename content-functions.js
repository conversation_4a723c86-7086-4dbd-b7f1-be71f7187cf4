// Content Functions for Forever Living Business Documentation

function getOverviewContent() {
    return `
        <div class="highlight-box">
            <h1 class="text-3xl font-bold mb-4">🚀 Forever Living Business Documentation</h1>
            <p class="text-lg opacity-90">Complete training guide for building a successful Forever Living business with proven strategies and actionable insights.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Modules</p>
                        <p class="text-2xl font-bold text-gray-900">6</p>
                    </div>
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Success Stories</p>
                        <p class="text-2xl font-bold text-gray-900">3</p>
                    </div>
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Action Items</p>
                        <p class="text-2xl font-bold text-gray-900">15+</p>
                    </div>
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <h2 class="text-2xl font-bold text-gray-900 mb-6">📚 Learning Modules</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="doc-card p-6 cursor-pointer" onclick="showSection('secret-formula')">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Secret Formula</h3>
                        <p class="text-sm text-gray-600">3C Strategy & Success Principles</p>
                    </div>
                </div>
                <p class="text-gray-600 text-sm">Learn Krishna Sir's proven 3C formula: Contribution, Connection, and Clarity for business success.</p>
                <div class="mt-4">
                    <span class="badge-success">Complete</span>
                </div>
            </div>

            <div class="doc-card p-6 cursor-pointer" onclick="showSection('training-system')">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Training System</h3>
                        <p class="text-sm text-gray-600">Daily Actions & Time Management</p>
                    </div>
                </div>
                <p class="text-gray-600 text-sm">Master the daily routine and system approach for consistent business growth and team development.</p>
                <div class="mt-4">
                    <span class="badge-success">Complete</span>
                </div>
            </div>

            <div class="doc-card p-6 cursor-pointer" onclick="showSection('success-stories')">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Success Stories</h3>
                        <p class="text-sm text-gray-600">Real Examples & Case Studies</p>
                    </div>
                </div>
                <p class="text-gray-600 text-sm">Learn from Faiz Alam and other successful leaders who built their Forever Living empire.</p>
                <div class="mt-4">
                    <span class="badge-success">Complete</span>
                </div>
            </div>

            <div class="doc-card p-6 cursor-pointer" onclick="showSection('level-up')">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Level Up Strategy</h3>
                        <p class="text-sm text-gray-600">Rank Advancement & Goals</p>
                    </div>
                </div>
                <p class="text-gray-600 text-sm">Strategic roadmap for advancing from Novas to Manager level with clear milestones.</p>
                <div class="mt-4">
                    <span class="badge-success">Complete</span>
                </div>
            </div>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-2">🎯 Quick Start Guide</h3>
            <p class="text-blue-800 mb-4">New to Forever Living business? Start with these essential modules:</p>
            <div class="space-y-2">
                <div class="flex items-center text-blue-700">
                    <span class="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold mr-3">1</span>
                    <span>Read the Secret Formula to understand core principles</span>
                </div>
                <div class="flex items-center text-blue-700">
                    <span class="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold mr-3">2</span>
                    <span>Study the Training System for daily implementation</span>
                </div>
                <div class="flex items-center text-blue-700">
                    <span class="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold mr-3">3</span>
                    <span>Review Success Stories for motivation and examples</span>
                </div>
                <div class="flex items-center text-blue-700">
                    <span class="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold mr-3">4</span>
                    <span>Create your Action Plan using the provided template</span>
                </div>
            </div>
        </div>
    `;
}

function getSecretFormulaContent() {
    return `
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">🔐 Sir Ka's Secret Formula</h1>
            <p class="text-lg text-gray-600 mb-6">Krishna Sir's proven 3C strategy for building a successful Forever Living business</p>
        </div>

        <div class="highlight-box mb-8">
            <h2 class="text-2xl font-bold mb-4">⚡ Core Principles</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold">1</span>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Excitement Full Power</h3>
                    <p class="text-sm opacity-90">Jab tak energy high hai, tab tak journey smooth hai</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold">2</span>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Clear WHY</h3>
                    <p class="text-sm opacity-90">Apna "Why" clearly define karo</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold">3</span>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">3C Formula</h3>
                    <p class="text-sm opacity-90">Contribution, Connection, Clarity</p>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div class="doc-card p-6">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">🤝 3C Formula Breakdown</h3>
                <div class="space-y-4">
                    <div class="border-l-4 border-blue-500 pl-4">
                        <h4 class="font-semibold text-gray-900">Contribution</h4>
                        <p class="text-gray-600 text-sm">Hamesha kisi na kisi ki help karo</p>
                    </div>
                    <div class="border-l-4 border-green-500 pl-4">
                        <h4 class="font-semibold text-gray-900">Connection</h4>
                        <p class="text-gray-600 text-sm">Team se bonding banao, logon se judho</p>
                    </div>
                    <div class="border-l-4 border-purple-500 pl-4">
                        <h4 class="font-semibold text-gray-900">Clarity → Focus → Direction</h4>
                        <p class="text-gray-600 text-sm">Jab clarity hogi, tabhi focus milega aur direction bhi</p>
                    </div>
                </div>
            </div>

            <div class="doc-card p-6">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">🎯 Level Up Roadmap</h3>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <span class="badge-info mr-3">Novas</span>
                        <span class="text-sm text-gray-600">Target: 2CC (AS level)</span>
                    </div>
                    <div class="flex items-center">
                        <span class="badge-warning mr-3">AS</span>
                        <span class="text-sm text-gray-600">Target: 10CC Supervisor</span>
                    </div>
                    <div class="flex items-center">
                        <span class="badge-success mr-3">Supervisor</span>
                        <span class="text-sm text-gray-600">Target: Assistant Manager</span>
                    </div>
                    <div class="flex items-center">
                        <span class="badge-success mr-3">Manager</span>
                        <span class="text-sm text-gray-600">Target: Senior Manager</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="doc-card p-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">🎁 Incentives & Recognition</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">4x7 Incentive</h4>
                    <p class="text-gray-600 text-sm mb-2">Jo bhi log <strong>har mahine 7 tareekh se pehle 4CC</strong> ka business close kar lete hain:</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Forever ki official magazine mein naam</li>
                        <li>• Recognition milta hai</li>
                        <li>• Special benefits</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">C9 Challenge - Fat to Fit</h4>
                    <p class="text-gray-600 text-sm">Apna transformation dikhao aur inspire karo</p>
                </div>
            </div>
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-yellow-900 mb-2">💡 Golden Rule</h3>
            <p class="text-yellow-800 font-medium mb-2">Forever ke daily products ka use khud karo</p>
            <p class="text-yellow-700 text-sm">Aur jab kisi ko business mein lao, toh <strong>pure knowledge aur confidence</strong> ke saath lao.</p>
            <p class="text-yellow-700 text-sm mt-2"><em>Forever ka khud ka alag wallet system bhi hota hai – usse bhi samjho aur samjhao.</em></p>
        </div>
    `;
}

function getTrainingSystemContent() {
    return `
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">🎯 Training System</h1>
            <p class="text-lg text-gray-600 mb-6">Daily actions and systematic approach for consistent business growth</p>
        </div>

        <div class="highlight-box mb-8">
            <h2 class="text-2xl font-bold mb-4">🔥 Take Charge of Your Journey</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <span class="w-2 h-2 bg-white rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span>Build your <strong>10CC</strong> with complete ownership</span>
                    </li>
                    <li class="flex items-start">
                        <span class="w-2 h-2 bg-white rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span>Personally <strong>generate leads, conduct meetings</strong></span>
                    </li>
                </ul>
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <span class="w-2 h-2 bg-white rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span>Implement learnings <strong>within 24 hours</strong></span>
                    </li>
                    <li class="flex items-start">
                        <span class="w-2 h-2 bg-white rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span>Build your <strong>personal brand</strong></span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div class="doc-card p-6">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">📅 Daily Action Plan</h3>
                <div class="space-y-4">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Control Your Time</h4>
                        <p class="text-gray-600 text-sm">Apne day ke CEO bano</p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Daily Value Addition</h4>
                        <p class="text-gray-600 text-sm">Har roz ek insaan ko value do - emotionally connect</p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Write Your WHY</h4>
                        <p class="text-gray-600 text-sm">Har din apne why ko likho - strengthen your purpose</p>
                    </div>
                </div>
            </div>

            <div class="doc-card p-6">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">⚙️ System Approach</h3>
                <div class="space-y-4">
                    <div class="border-l-4 border-blue-500 pl-4">
                        <h4 class="font-semibold text-gray-900">System se kaam lo</h4>
                        <p class="text-gray-600 text-sm">Superhero matt bano - step by step approach</p>
                    </div>
                    <div class="border-l-4 border-green-500 pl-4">
                        <h4 class="font-semibold text-gray-900">Track Actions</h4>
                        <p class="text-gray-600 text-sm">Roz apne actions ko time track karo</p>
                    </div>
                    <div class="border-l-4 border-purple-500 pl-4">
                        <h4 class="font-semibold text-gray-900">Training System</h4>
                        <p class="text-gray-600 text-sm">Training system ko importance do</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="doc-card p-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">📈 Level Up Dates</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center p-3 bg-blue-50 rounded-lg">
                    <div class="font-bold text-blue-600">5th July</div>
                </div>
                <div class="text-center p-3 bg-blue-50 rounded-lg">
                    <div class="font-bold text-blue-600">8th July</div>
                </div>
                <div class="text-center p-3 bg-blue-50 rounded-lg">
                    <div class="font-bold text-blue-600">13th July</div>
                </div>
                <div class="text-center p-3 bg-blue-50 rounded-lg">
                    <div class="font-bold text-blue-600">16th July</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg">
                    <div class="font-bold text-green-600">20th July</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg">
                    <div class="font-bold text-green-600">23rd July</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg">
                    <div class="font-bold text-green-600">26th July</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg">
                    <div class="font-bold text-green-600">28th July</div>
                </div>
            </div>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-green-900 mb-2">🎯 TASK: From Fat to Fit</h3>
            <p class="text-green-800 mb-4"><strong>1Kit = 1CC means 2Kit = 2CC (9 Kits)</strong></p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="font-semibold text-green-900 mb-2">Key Focus Areas:</h4>
                    <ul class="text-green-700 text-sm space-y-1">
                        <li>• Responsible → Learning</li>
                        <li>• Sacrifice for growth</li>
                        <li>• Consistency in part-time approach</li>
                        <li>• Instagram as business tool</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-green-900 mb-2">Product Strategy:</h4>
                    <p class="text-green-700 text-sm">Use the product and share the experience with your network, then share the business opportunity.</p>
                </div>
            </div>
        </div>
    `;
}

function getSuccessStoriesContent() {
    return `
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">⚡ Success Stories</h1>
            <p class="text-lg text-gray-600 mb-6">Learn from real examples of successful Forever Living leaders</p>
        </div>

        <div class="doc-card p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">🏆 Faiz Alam - Empire Builder</h2>
            <p class="text-lg text-gray-600 mb-6">How did Faiz Alam leave his Family Business and create an Empire?</p>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">📊 Key Achievements</h3>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <span class="badge-success mr-3">6 CC</span>
                            <span class="text-sm text-gray-600">Completed from Hot Market (June-July)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="badge-warning mr-3">4 Managers</span>
                            <span class="text-sm text-gray-600">All from Hot Market</span>
                        </div>
                        <div class="flex items-center">
                            <span class="badge-info mr-3">1 Soaring</span>
                            <span class="text-sm text-gray-600">Sakib (Faiz ke mama)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="badge-info mr-3">3 Senior</span>
                            <span class="text-sm text-gray-600">Sheetal, Kamlesh, Palash</span>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">🎯 Success Strategy</h3>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-start">
                            <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                            <span><strong>2CC Mandatory:</strong> 30,000 fixed investment</span>
                        </li>
                        <li class="flex items-start">
                            <span class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                            <span><strong>Hot Market Focus:</strong> Started with family & friends</span>
                        </li>
                        <li class="flex items-start">
                            <span class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                            <span><strong>Relation Building:</strong> Krishna Sir mentorship</span>
                        </li>
                        <li class="flex items-start">
                            <span class="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                            <span><strong>System Timing:</strong> New enrollment system launch</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 class="font-semibold text-blue-900 mb-2">🔑 Faiz's Secret</h4>
                <p class="text-blue-800 font-medium">"Kyun Nhi Kar Sakte… Believe in Yourself"</p>
            </div>
        </div>

        <div class="doc-card p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">🚀 Nouman Raza - Fastest Sapphire Manager</h2>
            <p class="text-gray-600 mb-6">From Assistant Supervisor to Sapphire Manager in record time</p>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">💡 Main Tips</h3>
                    <ul class="space-y-2 text-gray-600">
                        <li>• Marketing Plan + Product Knowledge</li>
                        <li>• Niyat Saaf Rakho (Pure Intentions)</li>
                        <li>• 2CC knowledge aur world best products</li>
                        <li>• Purpose Clear Karo</li>
                        <li>• Remove Laziness</li>
                        <li>• Make Schedule + Good Day Plan</li>
                        <li>• Support customers, don't exploit</li>
                        <li>• Create vision for downline</li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">🎯 Action Formula</h3>
                    <div class="space-y-3">
                        <div class="border-l-4 border-red-500 pl-4">
                            <h4 class="font-semibold text-gray-900">❌ 10 Callings</h4>
                            <p class="text-gray-600 text-sm">Aise nhi chalega</p>
                        </div>
                        <div class="border-l-4 border-green-500 pl-4">
                            <h4 class="font-semibold text-gray-900">✅ DO MORE</h4>
                            <p class="text-gray-600 text-sm">Consistency aur hard work</p>
                        </div>
                        <div class="border-l-4 border-blue-500 pl-4">
                            <h4 class="font-semibold text-gray-900">Practice These:</h4>
                            <p class="text-gray-600 text-sm">Invitation, Follow Up, Closing</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-yellow-900 mb-2">🏅 Achievers Club Benefits</h3>
            <p class="text-yellow-800 mb-4"><strong>Be Wise in Your Investment</strong></p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="font-semibold text-yellow-900 mb-2">For Assistant Supervisor:</h4>
                    <ul class="text-yellow-700 text-sm space-y-1">
                        <li>• Never Give Up</li>
                        <li>• Believe in Yourself</li>
                        <li>• Long Term Ka Game Hai</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-yellow-900 mb-2">For Supervisor:</h4>
                    <ul class="text-yellow-700 text-sm space-y-1">
                        <li>• Team development pe focus</li>
                        <li>• Forever incentives samjho</li>
                        <li>• Recurring income strategy</li>
                    </ul>
                </div>
            </div>
        </div>
    `;
}

function getLevelUpContent() {
    return `
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">📈 Level Up Strategy</h1>
            <p class="text-lg text-gray-600 mb-6">Strategic roadmap for advancing through Forever Living ranks</p>
        </div>

        <div class="highlight-box mb-8">
            <h2 class="text-2xl font-bold mb-4">🎯 Grand Level Up Session</h2>
            <p class="text-lg opacity-90 mb-4">By Rohit Arora (Achievers Club)</p>
            <p class="opacity-80">Ye session sirf unke liye hai jo apna minimum billing kar chuke hain</p>
        </div>

        <div class="doc-card p-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">🏆 2CC Combo Offer - 5 Benefits</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Personal Mentorship</h4>
                        <p class="text-gray-600 text-sm">Krishna Arora se roadmap + action planning</p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Achievers Club Course</h4>
                        <p class="text-gray-600 text-sm">Networking skills + personal branding</p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Upline Mentorship</h4>
                        <p class="text-gray-600 text-sm">Daily tracking + activities guidance</p>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="step-card">
                        <div class="step-number">4</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Daily Training System</h4>
                        <p class="text-gray-600 text-sm">Consistent skill development</p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">5</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Lead Generation System</h4>
                        <p class="text-gray-600 text-sm">Proven lead generation methods</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div class="doc-card p-6">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">💰 Manager Mindset</h3>
                <div class="space-y-4">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h4 class="font-semibold text-green-900 mb-2">Manager bante hi 1 Lakh ka check!</h4>
                    </div>
                    <ul class="space-y-2 text-gray-600">
                        <li>• Forever Products family ko gift karo</li>
                        <li>• Pehle Aloe Vera, baad mein iPhone</li>
                        <li>• One month mein income nhi aaye toh Don't Give Up</li>
                        <li>• Long term game hai - patience rakho</li>
                        <li>• Fly High in July - DO IT!</li>
                    </ul>
                </div>
            </div>

            <div class="doc-card p-6">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">🎯 Focus Areas</h3>
                <div class="space-y-3">
                    <div class="border-l-4 border-blue-500 pl-4">
                        <h4 class="font-semibold text-gray-900">LEVEL UP</h4>
                        <p class="text-gray-600 text-sm">Focus on one thing at a time</p>
                    </div>
                    <div class="border-l-4 border-green-500 pl-4">
                        <h4 class="font-semibold text-gray-900">FRONT CC</h4>
                        <p class="text-gray-600 text-sm">Direct customer acquisition</p>
                    </div>
                    <div class="border-l-4 border-purple-500 pl-4">
                        <h4 class="font-semibold text-gray-900">GOAL SETTING</h4>
                        <p class="text-gray-600 text-sm">Goal mapping & tracking</p>
                    </div>
                    <div class="border-l-4 border-yellow-500 pl-4">
                        <h4 class="font-semibold text-gray-900">TEAM COORDINATION</h4>
                        <p class="text-gray-600 text-sm">Build relationships with downline</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="doc-card p-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">📱 Social Media Strategy</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">Daily Activities:</h4>
                    <ul class="space-y-2 text-gray-600">
                        <li>• Daily 5 Instagram stories about work</li>
                        <li>• Personal pics every 3-4 days</li>
                        <li>• Social media updates regularly</li>
                        <li>• Content creation for business</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">Lead Generation:</h4>
                    <ul class="space-y-2 text-gray-600">
                        <li>• Daily DMs on Instagram</li>
                        <li>• Hot Market & Reference Market</li>
                        <li>• Har bande ko paise ki need hai</li>
                        <li>• Personal brand building</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="bg-red-50 border border-red-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-red-900 mb-2">⚠️ Common Reasons for Leaving Business</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <h4 class="font-semibold text-red-900 mb-2">Marketing Plan Issues</h4>
                    <p class="text-red-700 text-sm">Not understanding the compensation structure</p>
                </div>
                <div>
                    <h4 class="font-semibold text-red-900 mb-2">Company Policy</h4>
                    <p class="text-red-700 text-sm">Khud ke account pe paise nhi dalwane</p>
                </div>
                <div>
                    <h4 class="font-semibold text-red-900 mb-2">Product vs Prospects</h4>
                    <p class="text-red-700 text-sm">Focus on products, not just prospects</p>
                </div>
            </div>
        </div>
    `;
}

function getActionPlanContent() {
    return `
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">📋 Action Plan</h1>
            <p class="text-lg text-gray-600 mb-6">Your personalized roadmap to Forever Living success</p>
        </div>

        <div class="highlight-box mb-8">
            <h2 class="text-2xl font-bold mb-4">🎯 Important Self-Assessment Questions</h2>
            <p class="text-lg opacity-90">Answer these honestly to create your action plan</p>
        </div>

        <div class="doc-card p-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">❓ Key Questions to Ask Yourself</h3>
            <div class="space-y-4">
                <div class="border-l-4 border-blue-500 pl-4">
                    <h4 class="font-semibold text-gray-900">1. Why am I really here today?</h4>
                </div>
                <div class="border-l-4 border-green-500 pl-4">
                    <h4 class="font-semibold text-gray-900">2. What will change in my life if I take a decision today?</h4>
                </div>
                <div class="border-l-4 border-purple-500 pl-4">
                    <h4 class="font-semibold text-gray-900">3. What is stopping me from success till now?</h4>
                </div>
                <div class="border-l-4 border-yellow-500 pl-4">
                    <h4 class="font-semibold text-gray-900">4. If someone is guiding me with experience, will I trust and follow fully?</h4>
                </div>
                <div class="border-l-4 border-red-500 pl-4">
                    <h4 class="font-semibold text-gray-900">5. What's the cost of staying the same?</h4>
                </div>
                <div class="border-l-4 border-indigo-500 pl-4">
                    <h4 class="font-semibold text-gray-900">6. I am just one decision away from my 360 degree change - ready?</h4>
                </div>
                <div class="border-l-4 border-pink-500 pl-4">
                    <h4 class="font-semibold text-gray-900">7. If not now, then when?</h4>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div class="doc-card p-6">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">📅 30-Day Action Plan</h3>
                <div class="space-y-4">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Week 1: Foundation</h4>
                        <ul class="text-gray-600 text-sm space-y-1">
                            <li>• Complete 2CC billing</li>
                            <li>• Download My Forever App</li>
                            <li>• Complete KYC + Aadhaar</li>
                            <li>• Study product knowledge</li>
                        </ul>
                    </div>
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Week 2: Hot Market</h4>
                        <ul class="text-gray-600 text-sm space-y-1">
                            <li>• List 30 prospects</li>
                            <li>• Start with family & friends</li>
                            <li>• Share product experience</li>
                            <li>• Begin follow-ups</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="doc-card p-6">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">📊 Progress Tracking</h3>
                <div class="space-y-4">
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Week 3: Expansion</h4>
                        <ul class="text-gray-600 text-sm space-y-1">
                            <li>• 30 enrollments target</li>
                            <li>• Social media content</li>
                            <li>• Team building start</li>
                            <li>• Daily tracking</li>
                        </ul>
                    </div>
                    <div class="step-card">
                        <div class="step-number">4</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Week 4: Optimization</h4>
                        <ul class="text-gray-600 text-sm space-y-1">
                            <li>• Review and adjust</li>
                            <li>• Mentor feedback</li>
                            <li>• Plan next month</li>
                            <li>• Celebrate wins</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="doc-card p-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">📱 Daily Checklist</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">Morning Routine:</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="checkbox checkbox-primary mr-3">
                            <span class="text-gray-700">Write your WHY</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="checkbox checkbox-primary mr-3">
                            <span class="text-gray-700">Plan your day</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="checkbox checkbox-primary mr-3">
                            <span class="text-gray-700">Take Forever products</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="checkbox checkbox-primary mr-3">
                            <span class="text-gray-700">Review goals</span>
                        </label>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">Evening Review:</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="checkbox checkbox-primary mr-3">
                            <span class="text-gray-700">Track daily actions</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="checkbox checkbox-primary mr-3">
                            <span class="text-gray-700">Follow up with prospects</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="checkbox checkbox-primary mr-3">
                            <span class="text-gray-700">Social media posts</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="checkbox checkbox-primary mr-3">
                            <span class="text-gray-700">Plan tomorrow</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-green-900 mb-2">🎯 Next Steps</h3>
            <p class="text-green-800 mb-4">Ready to start your Forever Living journey?</p>
            <div class="flex flex-col sm:flex-row gap-4">
                <button class="btn btn-primary">Ask Your Mentor for Level Up Session</button>
                <button class="btn btn-outline">Download Action Plan Template</button>
            </div>
        </div>
    `;
}

function getQueryContent() {
    return `
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">❓ Questions & Answers</h1>
            <p class="text-lg text-gray-600 mb-6">Find answers to common questions about Forever Living business</p>
        </div>

        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-xl font-semibold text-gray-900">All Questions</h2>
                <p class="text-sm text-gray-600" id="qa-count">Loading questions...</p>
            </div>
            <button onclick="openAddQuestionModal()" class="btn btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add Question
            </button>
        </div>

        <div id="qa-container" class="space-y-6">
            <div class="flex items-center justify-center py-12">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p class="text-gray-600">Loading questions...</p>
                </div>
            </div>
        </div>

        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-2">💡 Need Help?</h3>
            <p class="text-blue-800 mb-4">Can't find the answer you're looking for? Feel free to add a new question!</p>
            <div class="flex flex-col sm:flex-row gap-3">
                <button onclick="openAddQuestionModal()" class="btn btn-outline btn-sm">
                    Ask a Question
                </button>
                <button onclick="showSection('overview')" class="btn btn-ghost btn-sm">
                    Back to Overview
                </button>
            </div>
        </div>
    `;
}

// Initialize charts function
function initializeCharts() {
    // Add chart initialization code here if needed
    console.log('Charts initialized');
}
