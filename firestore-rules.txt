rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read access to all questions
    match /questions/{questionId} {
      allow read: if true;

      // Allow create with basic validation
      allow create: if isValidQuestion(resource.data);

      // Prevent updates and deletes for security
      allow update, delete: if false;
    }

    // Deny access to all other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }

  // Helper function to validate question data
  function isValidQuestion(data) {
    return data.keys().hasAll(['question', 'answer']) &&
           data.question is string &&
           data.answer is string &&
           data.question.size() > 0 &&
           data.question.size() <= 1000 &&
           data.answer.size() > 0 &&
           data.answer.size() <= 5000;
  }
}
