# Firebase Setup Guide for Forever Living Q&A (Firestore)

## 1. Firebase Console Setup

### Step 1: Create Firestore Database
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `forever-47b8a`
3. Navigate to **Firestore Database** in the left sidebar
4. Click **Create Database**
5. Choose **Start in test mode** (we'll update rules later)
6. Select your preferred location (closest to your users)

### Step 2: Database Configuration
1. Firestore is automatically configured with your project ID
2. No additional URL configuration needed
3. The project ID `forever-47b8a` is already configured in the code

## 2. Security Rules Configuration

### Step 1: Apply Security Rules
1. In Firebase Console, go to **Firestore Database**
2. Click on the **Rules** tab
3. Replace the existing rules with the content from `firestore-rules.txt`
4. Click **Publish**

### Step 2: Rules Explanation
```javascript
rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read access to all questions
    match /questions/{questionId} {
      allow read: if true;                    // Anyone can read questions
      allow create: if isValidQuestion(resource.data);  // Only valid questions can be created
      allow update, delete: if false;        // Prevent modifications
    }

    // Deny access to all other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }

  // Helper function to validate question data
  function isValidQuestion(data) {
    return data.keys().hasAll(['question', 'answer', 'timestamp']) &&
           data.keys().hasOnly(['question', 'answer', 'timestamp']) &&
           data.question is string &&
           data.answer is string &&
           data.question.size() > 0 &&
           data.question.size() <= 500 &&      // Max 500 chars for question
           data.answer.size() > 0 &&
           data.answer.size() <= 2000 &&       // Max 2000 chars for answer
           data.timestamp == request.time;     // Must be server timestamp
  }
}
```

## 3. Database Structure

The Firestore database will store questions in this format:
```
Collection: questions
├── Document: auto-generated-id-1
│   ├── question: "How do I start with Forever Living?"
│   ├── answer: "To start with Forever Living, you need to..."
│   └── timestamp: Timestamp object
├── Document: auto-generated-id-2
│   ├── question: "What is the 3C Formula?"
│   ├── answer: "The 3C Formula consists of..."
│   └── timestamp: Timestamp object
└── ...
```

## 4. Security Features

### PIN Protection
- Only users with PIN "7890" can add questions
- PIN verification happens client-side before Firebase write
- Change PIN in the code if needed (search for "7890")

### Database Rules Protection
- Public read access for all questions
- Write access only for properly formatted data
- Validates question length (max 500 characters)
- Validates answer length (max 2000 characters)
- Requires timestamp to be server timestamp
- Prevents additional fields

## 5. Testing the Setup

### Test Read Access
1. Open the website
2. Navigate to Q&A section
3. Should load without errors (even if empty)

### Test Write Access
1. Click "Add Question" button
2. Fill in question and answer
3. Enter PIN: 7890
4. Question should be saved and appear in the list

## 6. Monitoring and Maintenance

### View Database Content
1. Go to Firebase Console > Firestore Database
2. Click on **Data** tab
3. Click on the `questions` collection to see all documents

### Monitor Usage
1. Go to **Usage** tab in Firestore Database
2. Monitor document reads/writes and storage usage

### Backup Data
1. Go to **Data** tab
2. Use the export functionality or set up automated backups
3. Consider using Firebase CLI for regular backups

## 7. Troubleshooting

### Common Issues

**Error: "Permission denied"**
- Check if security rules are properly applied
- Verify the database URL is correct

**Questions not loading**
- Check browser console for errors
- Verify Firebase CDN scripts are loaded
- Check network connectivity

**PIN not working**
- Verify PIN is exactly "7890"
- Check browser console for JavaScript errors

**Data not saving**
- Check security rules validation
- Verify question/answer length limits
- Check browser console for Firebase errors

## 8. Customization

### Change PIN
1. Find `const correctPin = "7890";` in index.html
2. Change to your desired PIN
3. Update this documentation

### Modify Validation Rules
1. Edit `firebase-rules.json`
2. Apply new rules in Firebase Console
3. Test thoroughly

### Add New Fields
1. Update database rules to allow new fields
2. Modify the `questionData` object in `saveQuestionToFirebase()`
3. Update the display function `displayQuestions()`

## 9. Production Checklist

- [ ] Security rules applied and tested
- [ ] PIN changed from default (if desired)
- [ ] Database URL configured correctly
- [ ] Read/write permissions tested
- [ ] Backup strategy in place
- [ ] Monitoring set up
- [ ] Error handling tested
