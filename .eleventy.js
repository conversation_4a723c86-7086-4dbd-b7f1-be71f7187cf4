const fs = require('fs');
const path = require('path');

module.exports = function(eleventyConfig) {
  // Copy static assets
  eleventyConfig.addPassthroughCopy("src/assets");
  eleventyConfig.addPassthroughCopy("src/js");
  eleventyConfig.addPassthroughCopy("src/css");
  eleventyConfig.addPassthroughCopy("extracted_pdf_content.json");
  
  // Watch for changes in CSS and JS
  eleventyConfig.addWatchTarget("src/css/");
  eleventyConfig.addWatchTarget("src/js/");
  eleventyConfig.addWatchTarget("extracted_pdf_content.json");
  
  // Add custom filters
  eleventyConfig.addFilter("dateFormat", function(date) {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  });
  
  eleventyConfig.addFilter("truncate", function(text, length = 150) {
    if (!text) return '';
    return text.length > length ? text.substring(0, length) + '...' : text;
  });
  
  eleventyConfig.addFilter("wordCount", function(text) {
    if (!text) return 0;
    return text.split(/\s+/).length;
  });
  
  eleventyConfig.addFilter("slug", function(text) {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  });
  
  // Add global data for PDF content
  eleventyConfig.addGlobalData("pdfData", function() {
    try {
      const data = fs.readFileSync('extracted_pdf_content.json', 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.warn('PDF data not found. Please run the PDF extraction script first.');
      return {
        extraction_date: new Date().toISOString(),
        total_files: 0,
        documents: []
      };
    }
  });
  
  // Custom collection for PDF documents
  eleventyConfig.addCollection("pdfDocuments", function(collectionApi) {
    try {
      const data = fs.readFileSync('extracted_pdf_content.json', 'utf8');
      const pdfData = JSON.parse(data);
      return pdfData.documents || [];
    } catch (error) {
      return [];
    }
  });
  
  // Add shortcode for Lottie animations
  eleventyConfig.addShortcode("lottie", function(src, width = "200", height = "200") {
    return `<lottie-player src="${src}" background="transparent" speed="1" style="width: ${width}px; height: ${height}px;" loop autoplay></lottie-player>`;
  });
  
  // Development server options
  eleventyConfig.setServerOptions({
    port: 3000,
    showAllHosts: true,
  });
  
  return {
    dir: {
      input: "src",
      output: "_site",
      includes: "_includes",
      layouts: "_layouts",
      data: "_data"
    },
    templateFormats: ["html", "njk", "md", "11ty.js"],
    htmlTemplateEngine: "njk",
    markdownTemplateEngine: "njk"
  };
};
